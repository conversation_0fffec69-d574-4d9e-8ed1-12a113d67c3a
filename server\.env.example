# Server Configuration
PORT=5000
NODE_ENV=development

# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----B<PERSON>IN PRIVATE KEY-----\nyour-private-key-here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d

# Email Configuration (Gmail with App Password)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password

# Crawler API Configuration
CRAWLER_API_URL=https://api.example.com/crawler
CRAWLER_API_KEY=your-crawler-api-key

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Templates
EMAIL_FROM_NAME=FactCheck Team
EMAIL_FROM_ADDRESS=<EMAIL>

# Security
BCRYPT_ROUNDS=12

# External APIs
FACT_CHECK_API_URL=https://factcheck-api.example.com
FACT_CHECK_API_KEY=your-fact-check-api-key

# VirusTotal API Configuration
VIRUSTOTAL_API_KEY=your-virustotal-api-key
VIRUSTOTAL_API_URL=https://www.virustotal.com/api/v3

# ScamAdviser API Configuration (via RapidAPI)
SCAMADVISER_API_KEY=your-rapidapi-key-for-scamadviser

# OpenAI GPT API Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.7

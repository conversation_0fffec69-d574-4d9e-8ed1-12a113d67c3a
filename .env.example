# FactCheck App Environment Variables Template
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=5002
NODE_ENV=development

# Database Configuration - Using Firestore
USE_FIRESTORE=true

# Firebase Configuration
FIREBASE_PROJECT_ID=factcheck-1d6e8
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_FIREBASE_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"

# OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=500
OPENAI_TEMPERATURE=0.7

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=7d

# VirusTotal API
VIRUSTOTAL_API_KEY=your-virustotal-api-key

# ScamAdviser API
SCAMADVISER_API_KEY=your-scamadviser-api-key

# News APIs (optional)
NEWSAPI_API_KEY=your-newsapi-key
NEWSDATA_API_KEY=your-newsdata-key

# Email Configuration (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password

# Frontend URL
FRONTEND_URL=http://localhost:3001

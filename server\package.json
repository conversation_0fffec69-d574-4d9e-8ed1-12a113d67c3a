{"name": "factcheck-backend", "version": "1.0.0", "description": "FactCheck Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "build": "echo 'No build step required'", "vercel-build": "echo 'No build step required'", "db:migrate": "node migrate-data.js", "db:verify": "node ../scripts/verify-firestore-data.js", "db:clear": "node ../scripts/verify-firestore-data.js clear", "test:local": "node ../scripts/test-local-deployment.js", "deploy:local": "npm run db:seed && npm run test:local"}, "dependencies": {"axios": "^1.5.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "firebase-admin": "^11.11.1", "helmet": "^7.0.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.4", "openai": "^4.104.0", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["factcheck", "api", "express", "postgresql"], "author": "FactCheck Team", "license": "MIT"}
{"name": "functions", "description": "Cloud Functions for Firebase - FactCheck", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "cors": "^2.8.5", "axios": "^1.5.0", "nodemailer": "^6.9.4", "express": "^4.18.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "openai": "^4.20.1"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0", "jest": "^29.6.2"}, "private": true}